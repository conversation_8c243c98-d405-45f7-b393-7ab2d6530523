#pragma once

#include "CoreMinimal.h"
#include "Modules/ModuleManager.h"
#include "Engine/Engine.h"
#include "Engine/World.h"
#include "Components/ActorComponent.h"
#include "Components/PrimitiveComponent.h"
#include "Components/StaticMeshComponent.h"
#include "Components/InstancedStaticMeshComponent.h"
#include "Components/HierarchicalInstancedStaticMeshComponent.h"
#include "Components/BrushComponent.h"
#include "Engine/StaticMesh.h"
#include "Materials/Material.h"
#include "Materials/MaterialInterface.h"
#include "Materials/MaterialInstance.h"
#include "Materials/MaterialInstanceDynamic.h"
#include "Engine/Texture2D.h"
#include "Engine/TextureRenderTarget2D.h"
#include "Landscape.h"
#include "LandscapeComponent.h"
#include "LandscapeProxy.h"
#include "LandscapeInfo.h"
#include "LandscapeLayerInfoObject.h"
#include "GameFramework/Actor.h"
#include "GameFramework/Pawn.h"
#include "GameFramework/PlayerController.h"
#include "GameFramework/GameModeBase.h"
#include "GameFramework/WorldSettings.h"
#include "UObject/UObjectGlobals.h"
#include "UObject/Package.h"
#include "UObject/ObjectMacros.h"
#include "UObject/Class.h"
#include "UObject/UObjectIterator.h"
#include "UObject/ConstructorHelpers.h"
#include "Kismet/GameplayStatics.h"
#include "Kismet/KismetMathLibrary.h"
#include "Kismet/KismetSystemLibrary.h"
#include "Math/UnrealMathUtility.h"
#include "Math/Vector.h"
#include "Math/Rotator.h"
#include "Math/Transform.h"
#include "Math/Box.h"
#include "Math/Sphere.h"
#include "Math/Plane.h"
#include "Math/Color.h"
#include "Math/IntVector.h"
#include "Math/RandomStream.h"
#include "HAL/Platform.h"
#include "HAL/PlatformFilemanager.h"
#include "HAL/FileManager.h"
#include "Misc/Paths.h"
#include "Misc/DateTime.h"
#include "Misc/Guid.h"
#include "Misc/SecureHash.h"
#include "Misc/Base64.h"
#include "Containers/Array.h"
#include "Containers/Map.h"
#include "Containers/Set.h"
#include "Containers/Queue.h"
#include "Containers/UnrealString.h"
#include "Templates/SharedPointer.h"
#include "Templates/UniquePtr.h"
#include "Templates/Function.h"
#include "Delegates/Delegate.h"


// Foliage System Core Includes
#include "InstancedFoliageActor.h"
#include "FoliageType.h"
#include "FoliageType_InstancedStaticMesh.h"
#include "FoliageType_Actor.h"
#include "FoliageInstancedStaticMeshComponent.h"
#include "GrassInstancedStaticMeshComponent.h"
#include "InteractiveFoliageActor.h"
#include "ProceduralFoliageComponent.h"
#include "ProceduralFoliageSpawner.h"
#include "ProceduralFoliageBlockingVolume.h"
#include "ProceduralFoliageVolume.h"
#include "FoliageStatistics.h"

// Foliage Edit Mode Includes (Editor Only) - UE 5.6 Compatible
#if WITH_EDITOR
#include "FoliageEditModule.h"
#include "FoliageHelper.h"
#include "Editor.h"
#include "EditorModeManager.h"
#include "LevelEditor.h"
#include "Toolkits/ToolkitManager.h"
#endif

// Landscape Integration
#include "LandscapeGrassType.h"
#include "LandscapeGizmoActiveActor.h"
#include "LandscapeSplineActor.h"
#include "LandscapeStreamingProxy.h"

// Rendering and Materials
#include "RenderingThread.h"
#include "RenderResource.h"
#include "GlobalShader.h"
#include "SceneView.h"
#include "PrimitiveSceneProxy.h"
#include "StaticMeshResources.h"

#include "LightMap.h"
#include "ShadowMap.h"

// Navigation System Integration
#include "NavigationSystem.h"
#include "NavigationData.h"
#include "NavigationOctree.h"
#include "NavMesh/RecastNavMesh.h"
#include "NavMesh/NavMeshBoundsVolume.h"

// AI Module Integration
// #include "AIController.h" // Include removed - not needed for foliage
// #include "BehaviorTree/BehaviorTree.h" // Include removed - not needed for foliage
// #include "BehaviorTree/BlackboardComponent.h" // Include removed - not needed for foliage
// #include "Perception/AIPerceptionComponent.h" // Include removed - not needed for foliage

// Physics and Collision
#include "PhysicsEngine/PhysicsSettings.h"
#include "PhysicsEngine/BodyInstance.h"
#include "PhysicsEngine/BodySetup.h"
#include "CollisionQueryParams.h"
#include "WorldCollision.h"
#include "Engine/HitResult.h"

// Geometry and Mesh Processing
#include "DynamicMesh/DynamicMesh3.h"
#include "DynamicMesh/DynamicMesh3.h"
#include "DynamicMesh/DynamicMeshAttributeSet.h"
// UE 5.6 Compatible - InteractiveToolsFramework path updated
#include "InteractiveToolsContext.h"
#include "ModelingComponents/Public/ModelingComponentsSettings.h"

// World Partition Integration
#include "WorldPartition/WorldPartition.h"
#include "WorldPartition/WorldPartitionSubsystem.h"
#include "WorldPartition/WorldPartitionStreamingSource.h"
#include "WorldPartition/WorldPartitionRuntimeHash.h"
#include "WorldPartition/WorldPartitionEditorHash.h"
#include "WorldPartition/ActorDescContainer.h"
#include "WorldPartition/WorldPartitionActorDesc.h"
#include "WorldPartition/WorldPartitionRuntimeCell.h"
#include "WorldPartition/WorldPartitionStreamingPolicy.h"

// Data Layers System
#include "WorldPartition/DataLayer/DataLayerSubsystem.h"
#include "WorldPartition/DataLayer/DataLayerManager.h"
#include "WorldPartition/DataLayer/DataLayerAsset.h"
#include "WorldPartition/DataLayer/DataLayerInstance.h"
#include "WorldPartition/DataLayer/WorldDataLayers.h"

// Level Instance Support
#include "LevelInstance/LevelInstanceTypes.h"
#include "LevelInstance/LevelInstanceInterface.h"
#include "LevelInstance/LevelInstanceActor.h"
#include "LevelInstance/LevelInstanceSubsystem.h"

// Python Integration (if available)
#if WITH_PYTHON
#include "PyWrapperTypeRegistry.h"
#include "PyWrapperObject.h"
#include "PyConversion.h"
#include "PyReferenceCollector.h"
#endif

// Async and Threading
#include "Async/Async.h"
#include "HAL/ThreadSafeBool.h"
#include "HAL/Event.h"
#include "HAL/Runnable.h"
#include "HAL/RunnableThread.h"
#include "Async/TaskGraphInterfaces.h"

// Logging and Profiling
#include "Logging/LogMacros.h"
#include "ProfilingDebugging/ScopedTimers.h"
#include "Stats/Stats.h"

// Declare stats group for Auracron Foliage Bridge
DECLARE_STATS_GROUP(TEXT("Auracron Foliage Bridge"), STATGROUP_AuracronFoliageBridge, STATCAT_Advanced);

// Declare cycle stats for performance monitoring
DECLARE_CYCLE_STAT_EXTERN(TEXT("Register Type"), STAT_AuracronFoliageBridge_RegisterType, STATGROUP_AuracronFoliageBridge, AURACRONFOLIAGEBRIDGE_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("Unregister Type"), STAT_AuracronFoliageBridge_UnregisterType, STATGROUP_AuracronFoliageBridge, AURACRONFOLIAGEBRIDGE_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("Add Instance"), STAT_AuracronFoliageBridge_AddInstance, STATGROUP_AuracronFoliageBridge, AURACRONFOLIAGEBRIDGE_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("Remove Instance"), STAT_AuracronFoliageBridge_RemoveInstance, STATGROUP_AuracronFoliageBridge, AURACRONFOLIAGEBRIDGE_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("Clear Instances"), STAT_AuracronFoliageBridge_ClearInstances, STATGROUP_AuracronFoliageBridge, AURACRONFOLIAGEBRIDGE_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("Get Instances"), STAT_AuracronFoliageBridge_GetInstances, STATGROUP_AuracronFoliageBridge, AURACRONFOLIAGEBRIDGE_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("Update Metrics"), STAT_AuracronFoliageBridge_UpdateMetrics, STATGROUP_AuracronFoliageBridge, AURACRONFOLIAGEBRIDGE_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("Update Instance"), STAT_AuracronFoliageBridge_UpdateInstance, STATGROUP_AuracronFoliageBridge, AURACRONFOLIAGEBRIDGE_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("Create Interactive"), STAT_AuracronFoliageBridge_CreateInteractive, STATGROUP_AuracronFoliageBridge, AURACRONFOLIAGEBRIDGE_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("Create Procedural Volume"), STAT_AuracronFoliageBridge_CreateProceduralVolume, STATGROUP_AuracronFoliageBridge, AURACRONFOLIAGEBRIDGE_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("Generate Procedural"), STAT_AuracronFoliageBridge_GenerateProcedural, STATGROUP_AuracronFoliageBridge, AURACRONFOLIAGEBRIDGE_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("Place Single"), STAT_AuracronFoliageBridge_PlaceSingle, STATGROUP_AuracronFoliageBridge, AURACRONFOLIAGEBRIDGE_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("Fill Area"), STAT_AuracronFoliageBridge_FillArea, STATGROUP_AuracronFoliageBridge, AURACRONFOLIAGEBRIDGE_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("Place On Spline"), STAT_AuracronFoliageBridge_PlaceOnSpline, STATGROUP_AuracronFoliageBridge, AURACRONFOLIAGEBRIDGE_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("Initialize"), STAT_AuracronFoliageBridge_Initialize, STATGROUP_AuracronFoliageBridge, AURACRONFOLIAGEBRIDGE_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("Shutdown"), STAT_AuracronFoliageBridge_Shutdown, STATGROUP_AuracronFoliageBridge, AURACRONFOLIAGEBRIDGE_API);
DECLARE_CYCLE_STAT_EXTERN(TEXT("Create Procedural Spawner"), STAT_AuracronFoliageBridge_CreateProceduralSpawner, STATGROUP_AuracronFoliageBridge, AURACRONFOLIAGEBRIDGE_API);

#include "AuracronFoliageBridge.generated.h"

// API Definition
#ifndef AURACRONFOLIAGEBRIDGE_API
#define AURACRONFOLIAGEBRIDGE_API DLLEXPORT
#endif

DECLARE_LOG_CATEGORY_EXTERN(LogAuracronFoliageBridge, Log, All);

// Additional statistics declarations for UE 5.6 - these are already declared above

// Forward Declarations
class UWorld;
class AActor;
class UActorComponent;
class UStaticMesh;
class UMaterialInterface;
class UFoliageType;
class UFoliageType_InstancedStaticMesh;
class UFoliageType_Actor;
class AInstancedFoliageActor;
class UFoliageInstancedStaticMeshComponent;
class UProceduralFoliageComponent;
class UProceduralFoliageSpawner;
class AProceduralFoliageVolume;
class AProceduralFoliageBlockingVolume;
class ALandscape;
class ULandscapeComponent;
class ULandscapeGrassType;
class AInteractiveFoliageActor;
class UNavigationSystemV1;
class UWorldPartition;
class UWorldPartitionSubsystem;
class UDataLayerSubsystem;
class ULevelInstanceSubsystem;

// Foliage Bridge Enums
UENUM(BlueprintType)
enum class EAuracronFoliageState : uint8
{
    Uninitialized       UMETA(DisplayName = "Uninitialized"),
    Initializing        UMETA(DisplayName = "Initializing"),
    Ready               UMETA(DisplayName = "Ready"),
    Processing          UMETA(DisplayName = "Processing"),
    Error               UMETA(DisplayName = "Error"),
    Shutdown            UMETA(DisplayName = "Shutdown")
};

UENUM(BlueprintType)
enum class EAuracronFoliageType : uint8
{
    StaticMesh          UMETA(DisplayName = "Static Mesh Foliage"),
    Actor               UMETA(DisplayName = "Actor Foliage"),
    Grass               UMETA(DisplayName = "Grass System"),
    Interactive         UMETA(DisplayName = "Interactive Foliage"),
    Procedural          UMETA(DisplayName = "Procedural Foliage")
};

UENUM(BlueprintType)
enum class EAuracronFoliageBridgePlacementMode : uint8
{
    Paint               UMETA(DisplayName = "Paint Mode"),
    Erase               UMETA(DisplayName = "Erase Mode"),
    Select              UMETA(DisplayName = "Select Mode"),
    Reapply             UMETA(DisplayName = "Reapply Mode"),
    Single              UMETA(DisplayName = "Single Mode"),
    Fill                UMETA(DisplayName = "Fill Mode"),
    Procedural          UMETA(DisplayName = "Procedural Mode")
};

UENUM(BlueprintType)
enum class EAuracronFoliageDensityLevel : uint8
{
    VeryLow             UMETA(DisplayName = "Very Low"),
    Low                 UMETA(DisplayName = "Low"),
    Medium              UMETA(DisplayName = "Medium"),
    High                UMETA(DisplayName = "High"),
    VeryHigh            UMETA(DisplayName = "Very High"),
    Ultra               UMETA(DisplayName = "Ultra"),
    Custom              UMETA(DisplayName = "Custom")
};

UENUM(BlueprintType)
enum class EAuracronFoliageCullingMode : uint8
{
    None                UMETA(DisplayName = "No Culling"),
    Distance            UMETA(DisplayName = "Distance Culling"),
    Frustum             UMETA(DisplayName = "Frustum Culling"),
    Occlusion           UMETA(DisplayName = "Occlusion Culling"),
    Combined            UMETA(DisplayName = "Combined Culling")
};

UENUM(BlueprintType)
enum class EAuracronFoliageBridgeLODMode : uint8
{
    Disabled            UMETA(DisplayName = "Disabled"),
    Simple              UMETA(DisplayName = "Simple LOD"),
    Advanced            UMETA(DisplayName = "Advanced LOD"),
    Hierarchical        UMETA(DisplayName = "Hierarchical LOD"),
    Adaptive            UMETA(DisplayName = "Adaptive LOD")
};

UENUM(BlueprintType)
enum class EAuracronFoliageCollisionMode : uint8
{
    None                UMETA(DisplayName = "No Collision"),
    Simple              UMETA(DisplayName = "Simple Collision"),
    Complex             UMETA(DisplayName = "Complex Collision"),
    Custom              UMETA(DisplayName = "Custom Collision")
};

UENUM(BlueprintType)
enum class EAuracronFoliageWindMode : uint8
{
    None                UMETA(DisplayName = "No Wind"),
    Simple              UMETA(DisplayName = "Simple Wind"),
    Advanced            UMETA(DisplayName = "Advanced Wind"),
    Procedural          UMETA(DisplayName = "Procedural Wind"),
    Interactive         UMETA(DisplayName = "Interactive Wind")
};

UENUM(BlueprintType)
enum class EAuracronFoliageSeasonMode : uint8
{
    None                UMETA(DisplayName = "No Seasons"),
    Spring              UMETA(DisplayName = "Spring"),
    Summer              UMETA(DisplayName = "Summer"),
    Autumn              UMETA(DisplayName = "Autumn"),
    Winter              UMETA(DisplayName = "Winter"),
    Dynamic             UMETA(DisplayName = "Dynamic Seasons")
};

UENUM(BlueprintType)
enum class EAuracronFoliageBiomeType : uint8
{
    Temperate           UMETA(DisplayName = "Temperate Forest"),
    Tropical            UMETA(DisplayName = "Tropical Rainforest"),
    Desert              UMETA(DisplayName = "Desert"),
    Tundra              UMETA(DisplayName = "Tundra"),
    Grassland           UMETA(DisplayName = "Grassland"),
    Wetland             UMETA(DisplayName = "Wetland"),
    Mountain            UMETA(DisplayName = "Mountain"),
    Coastal             UMETA(DisplayName = "Coastal"),
    Urban               UMETA(DisplayName = "Urban"),
    Custom              UMETA(DisplayName = "Custom Biome")
};

// Foliage Bridge Structs
USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronFoliageInstanceInfo
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Instance")
    FTransform Transform;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Instance")
    FVector Location;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Instance")
    FRotator Rotation;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Instance")
    FVector Scale;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Instance")
    int32 InstanceIndex;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Instance")
    bool bIsValid;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Instance")
    float Health;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Instance")
    float Age;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Instance")
    FLinearColor Color;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Instance")
    TMap<FString, float> CustomParameters;

    FAuracronFoliageInstanceInfo()
        : Transform(FTransform::Identity)
        , Location(FVector::ZeroVector)
        , Rotation(FRotator::ZeroRotator)
        , Scale(FVector::OneVector)
        , InstanceIndex(-1)
        , bIsValid(false)
        , Health(1.0f)
        , Age(0.0f)
        , Color(FLinearColor::White)
    {
    }
};

USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronFoliageTypeInfo
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Type")
    FString TypeName;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Type")
    EAuracronFoliageType FoliageType;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Type")
    TSoftObjectPtr<UStaticMesh> StaticMesh;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Type")
    TSoftClassPtr<AActor> ActorClass;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Type")
    TArray<TSoftObjectPtr<UMaterialInterface>> Materials;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Type")
    float Density;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Type")
    float Radius;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Type")
    FVector ScaleMin;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Type")
    FVector ScaleMax;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Type")
    bool bAlignToNormal;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Type")
    bool bRandomYaw;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Type")
    bool bUniformScale;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Type")
    EAuracronFoliageCollisionMode CollisionMode;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Type")
    EAuracronFoliageCullingMode CullingMode;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Type")
    EAuracronFoliageBridgeLODMode LODMode;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Type")
    TMap<FString, float> CustomSettings;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Type")
    bool bEnableCollision;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Type")
    float CullDistanceMin;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Type")
    float CullDistanceMax;

    FAuracronFoliageTypeInfo()
        : TypeName(TEXT(""))
        , FoliageType(EAuracronFoliageType::StaticMesh)
        , Density(1.0f)
        , Radius(100.0f)
        , ScaleMin(FVector(0.8f, 0.8f, 0.8f))
        , ScaleMax(FVector(1.2f, 1.2f, 1.2f))
        , bAlignToNormal(true)
        , bRandomYaw(true)
        , bUniformScale(false)
        , CollisionMode(EAuracronFoliageCollisionMode::Simple)
        , CullingMode(EAuracronFoliageCullingMode::Distance)
        , LODMode(EAuracronFoliageBridgeLODMode::Simple)
        , bEnableCollision(true)
        , CullDistanceMin(0.0f)
        , CullDistanceMax(10000.0f)
    {
    }
};

USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronFoliagePlacementInfo
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Placement")
    EAuracronFoliageBridgePlacementMode PlacementMode;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Placement")
    float BrushSize;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Placement")
    float PaintDensity;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Placement")
    float EraseDensity;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Placement")
    bool bFilterLandscape;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Placement")
    bool bFilterStaticMeshes;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Placement")
    bool bFilterBSP;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Placement")
    bool bFilterFoliage;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Placement")
    bool bFilterTranslucent;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Placement")
    float MinimumLayerWeight;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Placement")
    TArray<FName> LayerNames;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Placement")
    bool bInvertLayerWeights;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Placement")
    TMap<FString, bool> CustomFilters;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Placement")
    float PaintRadius;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Placement")
    int32 MinInstancesPerCluster;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Placement")
    int32 MaxInstancesPerCluster;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Placement")
    float MaxRotationVariance;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Placement")
    float MinScale;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Placement")
    float MaxScale;

    FAuracronFoliagePlacementInfo()
        : PlacementMode(EAuracronFoliageBridgePlacementMode::Paint)
        , BrushSize(512.0f)
        , PaintDensity(1.0f)
        , EraseDensity(1.0f)
        , bFilterLandscape(true)
        , bFilterStaticMeshes(true)
        , bFilterBSP(true)
        , bFilterFoliage(false)
        , bFilterTranslucent(false)
        , MinimumLayerWeight(0.5f)
        , bInvertLayerWeights(false)
        , PaintRadius(500.0f)
        , MinInstancesPerCluster(5)
        , MaxInstancesPerCluster(15)
        , MaxRotationVariance(15.0f)
        , MinScale(0.8f)
        , MaxScale(1.2f)
    {
    }
};

USTRUCT(BlueprintType)
struct AURACRONFOLIAGEBRIDGE_API FAuracronFoliagePerformanceInfo
{
    GENERATED_BODY()

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Performance")
    int32 TotalInstances;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Performance")
    int32 VisibleInstances;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Performance")
    int32 CulledInstances;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Performance")
    float RenderTime;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Performance")
    float UpdateTime;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Performance")
    float MemoryUsage;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Performance")
    int32 DrawCalls;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Performance")
    int32 Triangles;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Performance")
    float LODDistance;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Performance")
    float CullingDistance;

    UPROPERTY(BlueprintReadWrite, Category = "Foliage Performance")
    TMap<FString, float> CustomMetrics;

    FAuracronFoliagePerformanceInfo()
        : TotalInstances(0)
        , VisibleInstances(0)
        , CulledInstances(0)
        , RenderTime(0.0f)
        , UpdateTime(0.0f)
        , MemoryUsage(0.0f)
        , DrawCalls(0)
        , Triangles(0)
        , LODDistance(1000.0f)
        , CullingDistance(5000.0f)
    {
    }
};

// Foliage Bridge Delegates
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FAuracronFoliageStateChanged, EAuracronFoliageState, NewState);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FAuracronFoliageInstanceAdded, const FAuracronFoliageInstanceInfo&, InstanceInfo, const FString&, FoliageTypeName);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FAuracronFoliageInstanceRemoved, int32, InstanceIndex, const FString&, FoliageTypeName);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_ThreeParams(FAuracronFoliageInstanceModified, int32, InstanceIndex, const FAuracronFoliageInstanceInfo&, NewInstanceInfo, const FString&, FoliageTypeName);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FAuracronFoliageTypeRegistered, const FAuracronFoliageTypeInfo&, TypeInfo);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FAuracronFoliageTypeUnregistered, const FString&, TypeName);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_OneParam(FAuracronFoliagePerformanceUpdated, const FAuracronFoliagePerformanceInfo&, PerformanceInfo);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FAuracronFoliageOperationCompleted, const FString&, OperationName, bool, bSuccess);
DECLARE_DYNAMIC_MULTICAST_DELEGATE_TwoParams(FAuracronFoliageError, const FString&, ErrorMessage, int32, ErrorCode);

/**
 * Main API class for Auracron Foliage Bridge
 * Provides comprehensive interface for foliage management and procedural generation
 */
UCLASS(BlueprintType, Blueprintable)
class AURACRONFOLIAGEBRIDGE_API UAuracronFoliageBridgeAPI : public UObject
{
    GENERATED_BODY()

public:
    UAuracronFoliageBridgeAPI();
    virtual ~UAuracronFoliageBridgeAPI();

    // Core System Management
    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Core", CallInEditor)
    bool InitializeFoliageBridge(UWorld* TargetWorld = nullptr);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Core", CallInEditor)
    void ShutdownFoliageBridge();

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Core", CallInEditor)
    bool IsInitialized() const;

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Core", CallInEditor)
    EAuracronFoliageState GetCurrentState() const;

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Core", CallInEditor)
    UWorld* GetTargetWorld() const;

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Core", CallInEditor)
    void SetTargetWorld(UWorld* NewWorld);

    // Foliage Type Management
    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Types", CallInEditor)
    bool RegisterFoliageType(const FAuracronFoliageTypeInfo& TypeInfo);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Types", CallInEditor)
    bool UnregisterFoliageType(const FString& TypeName);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Types", CallInEditor)
    TArray<FString> GetRegisteredFoliageTypes() const;

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Types", CallInEditor)
    bool GetFoliageTypeInfo(const FString& TypeName, FAuracronFoliageTypeInfo& OutTypeInfo) const;

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Types", CallInEditor)
    bool UpdateFoliageTypeInfo(const FString& TypeName, const FAuracronFoliageTypeInfo& NewTypeInfo);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Types", CallInEditor)
    UFoliageType* GetUE5FoliageType(const FString& TypeName) const;

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Types", CallInEditor)
    UFoliageType_InstancedStaticMesh* CreateStaticMeshFoliageType(UStaticMesh* StaticMesh, const FString& TypeName);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Types", CallInEditor)
    UFoliageType_Actor* CreateActorFoliageType(UClass* ActorClass, const FString& TypeName);

    // Foliage Instance Management
    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Instances", CallInEditor)
    TArray<FAuracronFoliageInstanceInfo> GetFoliageInstances(const FString& TypeName);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Instances", CallInEditor)
    int32 GetFoliageInstanceCount(const FString& TypeName) const;

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Instances", CallInEditor)
    bool GetFoliageInstanceInfo(const FString& TypeName, int32 InstanceIndex, FAuracronFoliageInstanceInfo& OutInstanceInfo);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Instances", CallInEditor)
    bool UpdateFoliageInstance(const FString& TypeName, int32 InstanceIndex, const FAuracronFoliageInstanceInfo& NewInstanceInfo);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Instances", CallInEditor)
    bool RemoveFoliageInstance(const FString& TypeName, int32 InstanceIndex);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Instances", CallInEditor)
    int32 AddFoliageInstance(const FString& TypeName, const FAuracronFoliageInstanceInfo& InstanceInfo);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Instances", CallInEditor)
    TArray<int32> AddFoliageInstances(const FString& TypeName, const TArray<FAuracronFoliageInstanceInfo>& InstanceInfos);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Instances", CallInEditor)
    bool RemoveFoliageInstances(const FString& TypeName, const TArray<int32>& InstanceIndices);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Instances", CallInEditor)
    void ClearAllFoliageInstances(const FString& TypeName);

    // Foliage Placement and Painting
    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Placement", CallInEditor)
    bool PaintFoliageAtLocation(const FString& TypeName, const FVector& Location, const FAuracronFoliagePlacementInfo& PlacementInfo);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Placement", CallInEditor)
    bool EraseFoliageAtLocation(const FString& TypeName, const FVector& Location, const FAuracronFoliagePlacementInfo& PlacementInfo);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Placement", CallInEditor)
    TArray<int32> SelectFoliageAtLocation(const FString& TypeName, const FVector& Location, float Radius);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Placement", CallInEditor)
    bool ReapplyFoliageSettings(const FString& TypeName, const TArray<int32>& InstanceIndices);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Placement", CallInEditor)
    bool PlaceSingleFoliageInstance(const FString& TypeName, const FVector& Location, const FRotator& Rotation, const FVector& Scale);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Placement", CallInEditor)
    bool FillAreaWithFoliage(const FString& TypeName, const FBox& Area, const FAuracronFoliagePlacementInfo& PlacementInfo);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Placement", CallInEditor)
    bool PlaceFoliageOnSpline(const FString& TypeName, class USplineComponent* SplineComponent, const FAuracronFoliagePlacementInfo& PlacementInfo);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Placement", CallInEditor)
    bool PlaceFoliageOnLandscapeLayer(const FString& TypeName, const FName& LayerName, const FAuracronFoliagePlacementInfo& PlacementInfo);

    // Procedural Foliage Management
    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Procedural", CallInEditor)
    UProceduralFoliageSpawner* CreateProceduralFoliageSpawner(const FString& SpawnerName);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Procedural", CallInEditor)
    bool ConfigureProceduralFoliageSpawner(UProceduralFoliageSpawner* Spawner, const TArray<FAuracronFoliageTypeInfo>& FoliageTypes);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Procedural", CallInEditor)
    AProceduralFoliageVolume* CreateProceduralFoliageVolume(const FVector& Location, const FVector& Extent);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Procedural", CallInEditor)
    bool GenerateProceduralFoliage(AProceduralFoliageVolume* Volume, UProceduralFoliageSpawner* Spawner);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Procedural", CallInEditor)
    bool ClearProceduralFoliage(AProceduralFoliageVolume* Volume);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Procedural", CallInEditor)
    TArray<AProceduralFoliageVolume*> GetProceduralFoliageVolumes() const;

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Procedural", CallInEditor)
    bool SetProceduralFoliageDensity(const FString& TypeName, float NewDensity);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Procedural", CallInEditor)
    bool SetProceduralFoliageRadius(const FString& TypeName, float NewRadius);

    // Performance and Optimization
    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Performance", CallInEditor)
    FAuracronFoliagePerformanceInfo GetPerformanceInfo() const;

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Performance", CallInEditor)
    bool SetFoliageDensityScale(float DensityScale);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Performance", CallInEditor)
    float GetFoliageDensityScale() const;

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Performance", CallInEditor)
    bool SetFoliageCullingDistance(const FString& TypeName, float CullingDistance);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Performance", CallInEditor)
    bool SetFoliageLODDistance(const FString& TypeName, float LODDistance);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Performance", CallInEditor)
    bool EnableFoliageInstancing(const FString& TypeName, bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Performance", CallInEditor)
    bool OptimizeFoliageInstances(const FString& TypeName);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Performance", CallInEditor)
    void UpdateFoliagePerformanceMetrics();

    // Landscape Integration
    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Landscape", CallInEditor)
    bool AttachFoliageToLandscape(ALandscape* Landscape);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Landscape", CallInEditor)
    bool DetachFoliageFromLandscape(ALandscape* Landscape);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Landscape", CallInEditor)
    TArray<ALandscape*> GetAttachedLandscapes() const;

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Landscape", CallInEditor)
    bool CreateLandscapeGrassType(const FString& GrassTypeName, const TArray<FAuracronFoliageTypeInfo>& GrassVariants);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Landscape", CallInEditor)
    ULandscapeGrassType* GetLandscapeGrassType(const FString& GrassTypeName) const;

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Landscape", CallInEditor)
    bool ApplyGrassToLandscapeLayer(ALandscape* Landscape, const FName& LayerName, ULandscapeGrassType* GrassType);

    // Interactive Foliage
    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Interactive", CallInEditor)
    AInteractiveFoliageActor* CreateInteractiveFoliageActor(const FVector& Location, UStaticMesh* FoliageMesh);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Interactive", CallInEditor)
    bool ConfigureInteractiveFoliage(AInteractiveFoliageActor* FoliageActor, float Mass, float Damping, float SpringStrength);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Interactive", CallInEditor)
    TArray<AInteractiveFoliageActor*> GetInteractiveFoliageActors() const;

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Interactive", CallInEditor)
    bool EnableFoliageInteraction(const FString& TypeName, bool bEnable);

    // Wind and Animation
    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Wind", CallInEditor)
    bool SetFoliageWindSettings(const FString& TypeName, EAuracronFoliageWindMode WindMode, float WindStrength, float WindSpeed);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Wind", CallInEditor)
    bool EnableGlobalWindForFoliage(bool bEnable, const FVector& WindDirection, float WindStrength);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Wind", CallInEditor)
    bool CreateWindZone(const FVector& Location, float Radius, const FVector& WindDirection, float WindStrength);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Wind", CallInEditor)
    bool AnimateFoliageInstances(const FString& TypeName, const TArray<int32>& InstanceIndices, float AnimationSpeed);

    // Seasonal and Biome Systems
    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Seasons", CallInEditor)
    bool SetFoliageSeasonMode(const FString& TypeName, EAuracronFoliageSeasonMode SeasonMode);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Seasons", CallInEditor)
    bool TransitionFoliageToSeason(const FString& TypeName, EAuracronFoliageSeasonMode TargetSeason, float TransitionDuration);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Seasons", CallInEditor)
    bool CreateFoliageBiome(const FString& BiomeName, EAuracronFoliageBiomeType BiomeType, const TArray<FAuracronFoliageTypeInfo>& BiomeFoliageTypes);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Seasons", CallInEditor)
    bool ApplyBiomeToArea(const FString& BiomeName, const FBox& Area);

    // Collision and Physics
    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Collision", CallInEditor)
    bool SetFoliageCollisionSettings(const FString& TypeName, EAuracronFoliageCollisionMode CollisionMode, bool bGenerateOverlapEvents);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Collision", CallInEditor)
    bool EnableFoliagePhysics(const FString& TypeName, bool bEnable, float Mass, float LinearDamping, float AngularDamping);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Collision", CallInEditor)
    TArray<int32> GetFoliageInstancesInRadius(const FString& TypeName, const FVector& Center, float Radius) const;

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Collision", CallInEditor)
    TArray<int32> GetFoliageInstancesInBox(const FString& TypeName, const FBox& Box) const;

    // Material and Rendering
    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Materials", CallInEditor)
    bool SetFoliageMaterial(const FString& TypeName, int32 MaterialIndex, UMaterialInterface* Material);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Materials", CallInEditor)
    bool SetFoliageInstanceCustomData(const FString& TypeName, int32 InstanceIndex, const TArray<float>& CustomData);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Materials", CallInEditor)
    bool SetFoliageInstanceColor(const FString& TypeName, int32 InstanceIndex, const FLinearColor& Color);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Materials", CallInEditor)
    bool EnableFoliageShadowCasting(const FString& TypeName, bool bCastShadows, bool bCastDynamicShadows);

    // World Partition Integration
    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|WorldPartition", CallInEditor)
    bool EnableWorldPartitionSupport(bool bEnable);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|WorldPartition", CallInEditor)
    bool IsWorldPartitionEnabled() const;

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|WorldPartition", CallInEditor)
    bool SetFoliageStreamingDistance(const FString& TypeName, float StreamingDistance);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|WorldPartition", CallInEditor)
    bool AssignFoliageToDataLayer(const FString& TypeName, const FName& DataLayerName);

    // Debug and Visualization
    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Debug", CallInEditor)
    bool EnableFoliageDebugVisualization(bool bEnable, bool bShowBounds, bool bShowInstances, bool bShowStats);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Debug", CallInEditor)
    bool DrawFoliageDebugInfo(const FString& TypeName, bool bDrawBounds, bool bDrawTransforms, bool bDrawStats);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Debug", CallInEditor)
    FString GetFoliageDebugString(const FString& TypeName) const;

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Debug", CallInEditor)
    bool ValidateFoliageIntegrity(const FString& TypeName) const;

    // Utility Functions
    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Utility", CallInEditor)
    bool SaveFoliageConfiguration(const FString& ConfigurationName, const FString& FilePath);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Utility", CallInEditor)
    bool LoadFoliageConfiguration(const FString& ConfigurationName, const FString& FilePath);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Utility", CallInEditor)
    bool ExportFoliageData(const FString& TypeName, const FString& FilePath, const FString& Format);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Utility", CallInEditor)
    bool ImportFoliageData(const FString& TypeName, const FString& FilePath, const FString& Format);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Utility", CallInEditor)
    TArray<FString> GetSupportedExportFormats() const;

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Utility", CallInEditor)
    TArray<FString> GetSupportedImportFormats() const;

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Utility", CallInEditor)
    bool BackupFoliageData(const FString& BackupName);

    UFUNCTION(BlueprintCallable, Category = "Auracron Foliage Bridge|Utility", CallInEditor)
    bool RestoreFoliageData(const FString& BackupName);

    // Event Delegates
    UPROPERTY(BlueprintAssignable, Category = "Auracron Foliage Bridge|Events")
    FAuracronFoliageStateChanged OnStateChanged;

    UPROPERTY(BlueprintAssignable, Category = "Auracron Foliage Bridge|Events")
    FAuracronFoliageInstanceAdded OnInstanceAdded;

    UPROPERTY(BlueprintAssignable, Category = "Auracron Foliage Bridge|Events")
    FAuracronFoliageInstanceRemoved OnInstanceRemoved;

    UPROPERTY(BlueprintAssignable, Category = "Auracron Foliage Bridge|Events")
    FAuracronFoliageInstanceModified OnInstanceModified;

    UPROPERTY(BlueprintAssignable, Category = "Auracron Foliage Bridge|Events")
    FAuracronFoliageTypeRegistered OnTypeRegistered;

    UPROPERTY(BlueprintAssignable, Category = "Auracron Foliage Bridge|Events")
    FAuracronFoliageTypeUnregistered OnTypeUnregistered;

    UPROPERTY(BlueprintAssignable, Category = "Auracron Foliage Bridge|Events")
    FAuracronFoliagePerformanceUpdated OnPerformanceUpdated;

    UPROPERTY(BlueprintAssignable, Category = "Auracron Foliage Bridge|Events")
    FAuracronFoliageOperationCompleted OnOperationCompleted;

    UPROPERTY(BlueprintAssignable, Category = "Auracron Foliage Bridge|Events")
    FAuracronFoliageError OnError;

private:
    // Internal state management
    UPROPERTY()
    EAuracronFoliageState CurrentState;

    UPROPERTY()
    TObjectPtr<UWorld> TargetWorld;

    UPROPERTY()
    TMap<FString, TObjectPtr<UFoliageType>> RegisteredFoliageTypes;

    UPROPERTY()
    TMap<FString, FAuracronFoliageTypeInfo> FoliageTypeInfos;

    UPROPERTY()
    TMap<FString, TObjectPtr<AInstancedFoliageActor>> FoliageActors;

    UPROPERTY()
    FAuracronFoliagePerformanceInfo CachedPerformanceInfo;

    // Thread safety
    mutable FCriticalSection FoliageMutex;

    // Internal helper functions
    void UpdateState(EAuracronFoliageState NewState);
    AInstancedFoliageActor* GetOrCreateFoliageActor(const FString& TypeName);
    AInstancedFoliageActor* FindFoliageActor(const FString& TypeName) const;
    bool ValidateTypeName(const FString& TypeName) const;
    bool ValidateInstanceIndex(const FString& TypeName, int32 InstanceIndex) const;
    void LogError(const FString& ErrorMessage, int32 ErrorCode = 0) const;
    void LogWarning(const FString& WarningMessage) const;
    void LogInfo(const FString& InfoMessage) const;
};

class FAuracronFoliageBridgeModule : public IModuleInterface
{
public:
    /** IModuleInterface implementation */
    virtual void StartupModule() override;
    virtual void ShutdownModule() override;
};

